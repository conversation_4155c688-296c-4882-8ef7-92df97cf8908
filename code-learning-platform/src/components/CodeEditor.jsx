import { useRef, useEffect } from 'react'

const CodeEditor = ({ filename, content, onChange }) => {
  const textareaRef = useRef(null)

  const getLanguageClass = (filename) => {
    const extension = filename.split('.').pop().toLowerCase()
    return `language-${extension}`
  }

  const handleChange = (e) => {
    onChange(e.target.value)
  }

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.value = content
    }
  }, [filename, content])

  return (
    <div className="code-editor">
      <div className="editor-header">
        <span className="editor-filename">{filename}</span>
      </div>
      <textarea
        ref={textareaRef}
        className={`simple-editor ${getLanguageClass(filename)}`}
        value={content}
        onChange={handleChange}
        placeholder={`Start coding in ${filename}...`}
        spellCheck={false}
      />
    </div>
  )
}

export default CodeEditor

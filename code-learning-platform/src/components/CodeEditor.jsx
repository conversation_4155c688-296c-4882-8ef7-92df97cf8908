import { useRef, useEffect, useState } from 'react'
import * as monaco from 'monaco-editor'

const CodeEditor = ({ filename, content, onChange, fontSize = 14 }) => {
  const editorRef = useRef(null)
  const containerRef = useRef(null)
  const [isInitialized, setIsInitialized] = useState(false)

  const getLanguage = (filename) => {
    const extension = filename.split('.').pop().toLowerCase()
    switch (extension) {
      case 'html':
        return 'html'
      case 'css':
        return 'css'
      case 'js':
        return 'javascript'
      default:
        return 'plaintext'
    }
  }

  // Initialize Monaco Editor once
  useEffect(() => {
    if (containerRef.current && !editorRef.current) {
      editorRef.current = monaco.editor.create(containerRef.current, {
        value: content,
        language: getLanguage(filename),
        theme: 'vs-dark',
        automaticLayout: true,
        fontSize: fontSize,
        lineNumbers: 'on',
        minimap: { enabled: false },
        wordWrap: 'on',
        tabSize: 2,
        insertSpaces: true,
        scrollBeyondLastLine: false,
        renderWhitespace: 'selection',
        fontLigatures: true,
        cursorBlinking: 'blink',
        smoothScrolling: true
      })

      // Listen for content changes
      editorRef.current.onDidChangeModelContent(() => {
        if (isInitialized) {
          const value = editorRef.current.getValue()
          onChange(value)
        }
      })

      setIsInitialized(true)

      // Cleanup function
      return () => {
        if (editorRef.current) {
          editorRef.current.dispose()
          editorRef.current = null
        }
        setIsInitialized(false)
      }
    }
  }, [])

  // Handle file switching
  useEffect(() => {
    if (editorRef.current && isInitialized) {
      // Temporarily disable change events to prevent loops
      setIsInitialized(false)

      // Update content
      const currentValue = editorRef.current.getValue()
      if (currentValue !== content) {
        editorRef.current.setValue(content)
      }

      // Update language
      const model = editorRef.current.getModel()
      if (model) {
        const newLanguage = getLanguage(filename)
        monaco.editor.setModelLanguage(model, newLanguage)
      }

      // Re-enable change events
      setTimeout(() => setIsInitialized(true), 100)
    }
  }, [filename, content])

  // Handle font size changes
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.updateOptions({ fontSize: fontSize })
    }
  }, [fontSize])

  return (
    <div className="code-editor">
      <div className="editor-header">
        <span className="editor-filename">{filename}</span>
        <span className="editor-language">{getLanguage(filename).toUpperCase()}</span>
      </div>
      <div className="editor-container" ref={containerRef} />
    </div>
  )
}

export default CodeEditor

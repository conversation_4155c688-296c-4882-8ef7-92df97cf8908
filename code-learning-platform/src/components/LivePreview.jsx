import { useEffect, useRef } from 'react'

const LivePreview = ({ files }) => {
  const iframeRef = useRef(null)

  const generatePreviewHTML = () => {
    const htmlContent = files['index.html'] || '<h1>Welcome!</h1><p>Start coding in the HTML tab to see your changes here.</p>'
    const cssContent = files['style.css'] || ''
    const jsContent = files['script.js'] || ''
    const timestamp = new Date().toLocaleTimeString()

    // Always create a complete HTML document
    const previewHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Preview - ${timestamp}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .preview-timestamp {
            position: fixed;
            top: 5px;
            right: 5px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            z-index: 9999;
        }
        ${cssContent}
    </style>
</head>
<body>
    <div class="preview-timestamp">Updated: ${timestamp}</div>
    ${htmlContent}
    <script>
        // Wrap user JS in try-catch to prevent errors from breaking the preview
        try {
            ${jsContent}
        } catch (error) {
            console.error('JavaScript Error:', error);
        }
    </script>
</body>
</html>`

    return previewHTML
  }

  const updatePreview = () => {
    if (iframeRef.current) {
      const previewHTML = generatePreviewHTML()
      console.log('Updating preview...')

      // Use srcdoc for immediate update
      iframeRef.current.srcdoc = previewHTML
    }
  }

  useEffect(() => {
    // Update preview whenever files change
    console.log('Files changed, updating preview:', {
      html: files['index.html']?.substring(0, 50) + '...',
      css: files['style.css']?.substring(0, 50) + '...',
      js: files['script.js']?.substring(0, 50) + '...'
    })
    updatePreview()
  }, [files])

  // Also listen to individual file changes for more granular updates
  useEffect(() => {
    console.log('HTML content changed:', files['index.html']?.length || 0, 'characters')
    updatePreview()
  }, [files['index.html']])

  useEffect(() => {
    console.log('CSS content changed:', files['style.css']?.length || 0, 'characters')
    updatePreview()
  }, [files['style.css']])

  useEffect(() => {
    console.log('JS content changed:', files['script.js']?.length || 0, 'characters')
    updatePreview()
  }, [files['script.js']])

  // Initial load
  useEffect(() => {
    console.log('LivePreview component mounted')
    // Small delay to ensure iframe is ready
    setTimeout(updatePreview, 100)
  }, [])

  const refreshPreview = () => {
    updatePreview()
  }

  return (
    <div className="live-preview">
      <div className="preview-header">
        <span>Live Preview</span>
        <button 
          onClick={refreshPreview}
          className="refresh-button"
          title="Refresh Preview"
        >
          🔄 Refresh
        </button>
      </div>
      <iframe
        ref={iframeRef}
        className="preview-iframe"
        title="Live Preview"
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
        onLoad={() => console.log('Iframe loaded')}
      />
    </div>
  )
}

export default LivePreview

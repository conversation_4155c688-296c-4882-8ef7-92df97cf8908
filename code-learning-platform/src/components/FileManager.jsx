import React from 'react'

const FileManager = ({ files, activeFile, onFileSelect, fontSize, onFontSizeChange }) => {
  const getFileIcon = (filename) => {
    const extension = filename.split('.').pop().toLowerCase()
    switch (extension) {
      case 'html':
        return '🌐'
      case 'css':
        return '🎨'
      case 'js':
        return '⚡'
      default:
        return '📄'
    }
  }

  const getFileLanguage = (filename) => {
    const extension = filename.split('.').pop().toLowerCase()
    switch (extension) {
      case 'html':
        return 'HTML'
      case 'css':
        return 'CSS'
      case 'js':
        return 'JavaScript'
      default:
        return 'Text'
    }
  }

  const increaseFontSize = () => {
    const newSize = Math.min(fontSize + 2, 24) // Max font size 24px
    onFontSizeChange(newSize)
  }

  const decreaseFontSize = () => {
    const newSize = Math.max(fontSize - 2, 10) // Min font size 10px
    onFontSizeChange(newSize)
  }

  return (
    <div className="file-manager">
      <div className="file-tabs">
        {Object.keys(files).map((filename) => (
          <button
            key={filename}
            className={`file-tab ${activeFile === filename ? 'active' : ''}`}
            onClick={() => onFileSelect(filename)}
            title={`${getFileLanguage(filename)} - ${filename}`}
          >
            <span className="file-icon">{getFileIcon(filename)}</span>
            <span className="file-name">{filename}</span>
          </button>
        ))}

        <div className="font-size-controls">
          <button
            className="font-size-btn"
            onClick={decreaseFontSize}
            title="Decrease font size"
            disabled={fontSize <= 10}
          >
            A-
          </button>
          <span className="font-size-display">{fontSize}px</span>
          <button
            className="font-size-btn"
            onClick={increaseFontSize}
            title="Increase font size"
            disabled={fontSize >= 24}
          >
            A+
          </button>
        </div>
      </div>
    </div>
  )
}

export default FileManager

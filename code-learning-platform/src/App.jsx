import { useState } from 'react'
import CodeEditor from './components/CodeEditor'
import LivePreview from './components/LivePreview'
import FileManager from './components/FileManager'
import './App.css'

function App() {
  const [files, setFiles] = useState({
    'index.html': `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Web Page</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <h1>Hello, World!</h1>
    <p>Welcome to your HTML, CSS, and JavaScript learning playground!</p>
    <button id="myButton">Click me!</button>
    <script src="script.js"></script>
</body>
</html>`,
    'style.css': `body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f0f0f0;
}

h1 {
    color: #333;
    text-align: center;
}

p {
    color: #666;
    text-align: center;
    font-size: 18px;
}

button {
    display: block;
    margin: 20px auto;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #0056b3;
}`,
    'script.js': `document.addEventListener('DOMContentLoaded', function() {
    const button = document.getElementById('myButton');

    button.addEventListener('click', function() {
        alert('Hello from JavaScript!');

        // Change the button text
        this.textContent = 'Clicked!';

        // Change the background color
        document.body.style.backgroundColor = '#e8f4fd';
    });
});`
  })

  const [activeFile, setActiveFile] = useState('index.html')
  const [fontSize, setFontSize] = useState(14)

  const updateFile = (filename, content) => {
    setFiles(prev => ({
      ...prev,
      [filename]: content
    }))
  }

  const handleFontSizeChange = (newSize) => {
    setFontSize(newSize)
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>HTML, CSS & JavaScript Learning Platform</h1>
        <p>Code on the left, see results on the right!</p>
      </header>

      <div className="app-content">
        <div className="editor-panel">
          <FileManager
            files={files}
            activeFile={activeFile}
            onFileSelect={setActiveFile}
            fontSize={fontSize}
            onFontSizeChange={handleFontSizeChange}
          />
          <CodeEditor
            filename={activeFile}
            content={files[activeFile]}
            onChange={(content) => updateFile(activeFile, content)}
            fontSize={fontSize}
          />
        </div>

        <div className="preview-panel">
          <LivePreview files={files} />
        </div>
      </div>
    </div>
  )
}

export default App

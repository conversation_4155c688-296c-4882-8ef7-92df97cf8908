.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
  color: #ffffff;
}

.app-header {
  background-color: #2d2d30;
  padding: 1rem 2rem;
  border-bottom: 1px solid #3e3e42;
  text-align: center;
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  color: #ffffff;
}

.app-header p {
  margin: 0.5rem 0 0 0;
  color: #cccccc;
  font-size: 0.9rem;
}

.app-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #3e3e42;
  background-color: #1e1e1e;
}

.preview-panel {
  flex: 1;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

/* File Manager Styles */
.file-manager {
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  padding: 0;
}

.file-tabs {
  display: flex;
  overflow-x: auto;
}

.file-tab {
  padding: 0.75rem 1rem;
  background-color: #2d2d30;
  border: none;
  color: #cccccc;
  cursor: pointer;
  border-right: 1px solid #3e3e42;
  white-space: nowrap;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.file-tab:hover {
  background-color: #3e3e42;
}

.file-tab.active {
  background-color: #1e1e1e;
  color: #ffffff;
}

/* Code Editor Styles */
.code-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-header {
  background-color: #2d2d30;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #3e3e42;
  font-size: 0.85rem;
  color: #cccccc;
}

.editor-filename {
  font-weight: 500;
}

.editor-container {
  flex: 1;
  position: relative;
}

.simple-editor {
  width: 100%;
  height: 100%;
  background-color: #1e1e1e;
  color: #d4d4d4;
  border: none;
  outline: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 1rem;
  resize: none;
  tab-size: 2;
}

/* Live Preview Styles */
.live-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.preview-header {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dee2e6;
  color: #495057;
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.refresh-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: #0056b3;
}

.preview-iframe {
  flex: 1;
  border: none;
  background-color: #ffffff;
}

/* File Tab Icons */
.file-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.file-icon {
  font-size: 0.9rem;
}

.file-name {
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-content {
    flex-direction: column;
  }

  .editor-panel {
    border-right: none;
    border-bottom: 1px solid #3e3e42;
  }

  .file-tabs {
    justify-content: center;
  }
}

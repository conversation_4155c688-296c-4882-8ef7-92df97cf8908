{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/powershell/powershell.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/powershell/powershell.ts\nvar conf = {\n  // the default separators except `$-`\n  wordPattern: /(-?\\d*\\.\\d\\w*)|([^\\`\\~\\!\\@\\#%\\^\\&\\*\\(\\)\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>\\/\\?\\s]+)/g,\n  comments: {\n    lineComment: \"#\",\n    blockComment: [\"<#\", \"#>\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  ignoreCase: true,\n  tokenPostfix: \".ps1\",\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" }\n  ],\n  keywords: [\n    \"begin\",\n    \"break\",\n    \"catch\",\n    \"class\",\n    \"continue\",\n    \"data\",\n    \"define\",\n    \"do\",\n    \"dynamicparam\",\n    \"else\",\n    \"elseif\",\n    \"end\",\n    \"exit\",\n    \"filter\",\n    \"finally\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"function\",\n    \"if\",\n    \"in\",\n    \"param\",\n    \"process\",\n    \"return\",\n    \"switch\",\n    \"throw\",\n    \"trap\",\n    \"try\",\n    \"until\",\n    \"using\",\n    \"var\",\n    \"while\",\n    \"workflow\",\n    \"parallel\",\n    \"sequence\",\n    \"inlinescript\",\n    \"configuration\"\n  ],\n  helpKeywords: /SYNOPSIS|DESCRIPTION|PARAMETER|EXAMPLE|INPUTS|OUTPUTS|NOTES|LINK|COMPONENT|ROLE|FUNCTIONALITY|FORWARDHELPTARGETNAME|FORWARDHELPCATEGORY|REMOTEHELPRUNSPACE|EXTERNALHELP/,\n  // we include these common regular expressions\n  symbols: /[=><!~?&%|+\\-*\\/\\^;\\.,]+/,\n  escapes: /`(?:[abfnrtv\\\\\"'$]|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // commands and keywords\n      [\n        /[a-zA-Z_][\\w-]*/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // whitespace\n      [/[ \\t\\r\\n]+/, \"\"],\n      // labels\n      [/^:\\w*/, \"metatag\"],\n      // variables\n      [\n        /\\$(\\{((global|local|private|script|using):)?[\\w]+\\}|((global|local|private|script|using):)?[\\w]+)/,\n        \"variable\"\n      ],\n      // Comments\n      [/<#/, \"comment\", \"@comment\"],\n      [/#.*$/, \"comment\"],\n      // delimiters\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, \"delimiter\"],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/0[xX][0-9a-fA-F_]*[0-9a-fA-F]/, \"number.hex\"],\n      [/\\d+?/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings:\n      [/\\@\"/, \"string\", '@herestring.\"'],\n      [/\\@'/, \"string\", \"@herestring.'\"],\n      [\n        /\"/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: '@string.\"' }\n          }\n        }\n      ],\n      [\n        /'/,\n        {\n          cases: {\n            \"@eos\": \"string\",\n            \"@default\": { token: \"string\", next: \"@string.'\" }\n          }\n        }\n      ]\n    ],\n    string: [\n      [\n        /[^\"'\\$`]+/,\n        {\n          cases: {\n            \"@eos\": { token: \"string\", next: \"@popall\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /@escapes/,\n        {\n          cases: {\n            \"@eos\": { token: \"string.escape\", next: \"@popall\" },\n            \"@default\": \"string.escape\"\n          }\n        }\n      ],\n      [\n        /`./,\n        {\n          cases: {\n            \"@eos\": {\n              token: \"string.escape.invalid\",\n              next: \"@popall\"\n            },\n            \"@default\": \"string.escape.invalid\"\n          }\n        }\n      ],\n      [\n        /\\$[\\w]+$/,\n        {\n          cases: {\n            '$S2==\"': { token: \"variable\", next: \"@popall\" },\n            \"@default\": { token: \"string\", next: \"@popall\" }\n          }\n        }\n      ],\n      [\n        /\\$[\\w]+/,\n        {\n          cases: {\n            '$S2==\"': \"variable\",\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [\n        /[\"']/,\n        {\n          cases: {\n            \"$#==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": {\n              cases: {\n                \"@eos\": { token: \"string\", next: \"@popall\" },\n                \"@default\": \"string\"\n              }\n            }\n          }\n        }\n      ]\n    ],\n    herestring: [\n      [\n        /^\\s*([\"'])@/,\n        {\n          cases: {\n            \"$1==$S2\": { token: \"string\", next: \"@pop\" },\n            \"@default\": \"string\"\n          }\n        }\n      ],\n      [/[^\\$`]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/`./, \"string.escape.invalid\"],\n      [\n        /\\$[\\w]+/,\n        {\n          cases: {\n            '$S2==\"': \"variable\",\n            \"@default\": \"string\"\n          }\n        }\n      ]\n    ],\n    comment: [\n      [/[^#\\.]+/, \"comment\"],\n      [/#>/, \"comment\", \"@pop\"],\n      [/(\\.)(@helpKeywords)(?!\\w)/, { token: \"comment.keyword.$2\" }],\n      [/[\\.#]/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA;AAAA,EAET,aAAa;AAAA,EACb,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,QAAQ,EAAE;AAAA,IAC3C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,iBAAiB;AAAA,MACnC,KAAK,IAAI,OAAO,oBAAoB;AAAA,IACtC;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,IACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,IACnD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA;AAAA,EAEd,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,cAAc,EAAE;AAAA;AAAA,MAEjB,CAAC,SAAS,SAAS;AAAA;AAAA,MAEnB;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,MAAM,WAAW,UAAU;AAAA,MAC5B,CAAC,QAAQ,SAAS;AAAA;AAAA,MAElB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,WAAW;AAAA;AAAA,MAExB,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,iCAAiC,YAAY;AAAA,MAC9C,CAAC,QAAQ,QAAQ;AAAA;AAAA,MAEjB,CAAC,SAAS,WAAW;AAAA;AAAA,MAErB,CAAC,OAAO,UAAU,eAAe;AAAA,MACjC,CAAC,OAAO,UAAU,eAAe;AAAA,MACjC;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,YACR,YAAY,EAAE,OAAO,UAAU,MAAM,YAAY;AAAA,UACnD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ,EAAE,OAAO,iBAAiB,MAAM,UAAU;AAAA,YAClD,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,QAAQ;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU,EAAE,OAAO,YAAY,MAAM,UAAU;AAAA,YAC/C,YAAY,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU;AAAA,YACV,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,cACV,OAAO;AAAA,gBACL,QAAQ,EAAE,OAAO,UAAU,MAAM,UAAU;AAAA,gBAC3C,YAAY;AAAA,cACd;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,YAAY;AAAA,MACV;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,WAAW,EAAE,OAAO,UAAU,MAAM,OAAO;AAAA,YAC3C,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,MAAM,uBAAuB;AAAA,MAC9B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,UAAU;AAAA,YACV,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,MAAM,WAAW,MAAM;AAAA,MACxB,CAAC,6BAA6B,EAAE,OAAO,qBAAqB,CAAC;AAAA,MAC7D,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,EACF;AACF;", "names": []}
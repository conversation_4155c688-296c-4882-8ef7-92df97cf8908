{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/qsharp/qsharp.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/qsharp/qsharp.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\"\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"]\n  ],\n  autoClosingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' }\n  ]\n};\nvar language = {\n  // Set defaultToken to invalid to see what you do not tokenize yet\n  keywords: [\n    \"namespace\",\n    \"open\",\n    \"import\",\n    \"export\",\n    \"as\",\n    \"operation\",\n    \"function\",\n    \"body\",\n    \"adjoint\",\n    \"newtype\",\n    \"struct\",\n    \"controlled\",\n    \"if\",\n    \"elif\",\n    \"else\",\n    \"repeat\",\n    \"until\",\n    \"fixup\",\n    \"for\",\n    \"in\",\n    \"while\",\n    \"return\",\n    \"fail\",\n    \"within\",\n    \"apply\",\n    \"Adjoint\",\n    \"Controlled\",\n    \"Adj\",\n    \"Ctl\",\n    \"is\",\n    \"self\",\n    \"auto\",\n    \"distribute\",\n    \"invert\",\n    \"intrinsic\",\n    \"let\",\n    \"set\",\n    \"w/\",\n    \"new\",\n    \"not\",\n    \"and\",\n    \"or\",\n    \"use\",\n    \"borrow\",\n    \"using\",\n    \"borrowing\",\n    \"mutable\",\n    \"internal\"\n  ],\n  typeKeywords: [\n    \"Unit\",\n    \"Int\",\n    \"BigInt\",\n    \"Double\",\n    \"Bool\",\n    \"String\",\n    \"Qubit\",\n    \"Result\",\n    \"Pauli\",\n    \"Range\"\n  ],\n  invalidKeywords: [\n    \"abstract\",\n    \"base\",\n    \"bool\",\n    \"break\",\n    \"byte\",\n    \"case\",\n    \"catch\",\n    \"char\",\n    \"checked\",\n    \"class\",\n    \"const\",\n    \"continue\",\n    \"decimal\",\n    \"default\",\n    \"delegate\",\n    \"do\",\n    \"double\",\n    \"enum\",\n    \"event\",\n    \"explicit\",\n    \"extern\",\n    \"finally\",\n    \"fixed\",\n    \"float\",\n    \"foreach\",\n    \"goto\",\n    \"implicit\",\n    \"int\",\n    \"interface\",\n    \"lock\",\n    \"long\",\n    \"null\",\n    \"object\",\n    \"operator\",\n    \"out\",\n    \"override\",\n    \"params\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"ref\",\n    \"sbyte\",\n    \"sealed\",\n    \"short\",\n    \"sizeof\",\n    \"stackalloc\",\n    \"static\",\n    \"string\",\n    \"switch\",\n    \"this\",\n    \"throw\",\n    \"try\",\n    \"typeof\",\n    \"unit\",\n    \"ulong\",\n    \"unchecked\",\n    \"unsafe\",\n    \"ushort\",\n    \"virtual\",\n    \"void\",\n    \"volatile\"\n  ],\n  constants: [\"true\", \"false\", \"PauliI\", \"PauliX\", \"PauliY\", \"PauliZ\", \"One\", \"Zero\"],\n  builtin: [\n    \"X\",\n    \"Y\",\n    \"Z\",\n    \"H\",\n    \"HY\",\n    \"S\",\n    \"T\",\n    \"SWAP\",\n    \"CNOT\",\n    \"CCNOT\",\n    \"MultiX\",\n    \"R\",\n    \"RFrac\",\n    \"Rx\",\n    \"Ry\",\n    \"Rz\",\n    \"R1\",\n    \"R1Frac\",\n    \"Exp\",\n    \"ExpFrac\",\n    \"Measure\",\n    \"M\",\n    \"MultiM\",\n    \"Message\",\n    \"Length\",\n    \"Assert\",\n    \"AssertProb\",\n    \"AssertEqual\"\n  ],\n  operators: [\n    \"and=\",\n    \"<-\",\n    \"->\",\n    \"*\",\n    \"*=\",\n    \"@\",\n    \"!\",\n    \"^\",\n    \"^=\",\n    \":\",\n    \"::\",\n    \".\",\n    \"..\",\n    \"==\",\n    \"...\",\n    \"=\",\n    \"=>\",\n    \">\",\n    \">=\",\n    \"<\",\n    \"<=\",\n    \"-\",\n    \"-=\",\n    \"!=\",\n    \"or=\",\n    \"%\",\n    \"%=\",\n    \"|\",\n    \"+\",\n    \"+=\",\n    \"?\",\n    \"/\",\n    \"/=\",\n    \"&&&\",\n    \"&&&=\",\n    \"^^^\",\n    \"^^^=\",\n    \">>>\",\n    \">>>=\",\n    \"<<<\",\n    \"<<<=\",\n    \"|||\",\n    \"|||=\",\n    \"~~~\",\n    \"_\",\n    \"w/\",\n    \"w/=\"\n  ],\n  namespaceFollows: [\"namespace\", \"open\"],\n  importsFollows: [\"import\"],\n  symbols: /[=><!~?:&|+\\-*\\/\\^%@._]+/,\n  escapes: /\\\\[\\s\\S]/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // identifiers and keywords\n      [\n        /[a-zA-Z_$][\\w$]*/,\n        {\n          cases: {\n            \"@namespaceFollows\": {\n              token: \"keyword.$0\",\n              next: \"@namespace\"\n            },\n            \"@importsFollows\": {\n              token: \"keyword.$0\",\n              next: \"@imports\"\n            },\n            \"@typeKeywords\": \"type\",\n            \"@keywords\": \"keyword\",\n            \"@constants\": \"constant\",\n            \"@builtin\": \"keyword\",\n            \"@invalidKeywords\": \"invalid\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      // whitespace\n      { include: \"@whitespace\" },\n      // delimiters and operators\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/@symbols/, { cases: { \"@operators\": \"operator\", \"@default\": \"\" } }],\n      // numbers\n      [/\\d*\\.\\d+([eE][\\-+]?\\d+)?/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // delimiter: after number because of .\\d floats\n      [/[;,.]/, \"delimiter\"],\n      // strings\n      //[/\"([^\"\\\\]|\\\\.)*$/, 'string.invalid' ],  // non-terminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    namespace: [\n      { include: \"@whitespace\" },\n      [/[A-Za-z]\\w*/, \"namespace\"],\n      [/[\\.]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    imports: [\n      { include: \"@whitespace\" },\n      [/[A-Za-z]\\w*(?=\\.)/, \"namespace\"],\n      [/[A-Za-z]\\w*/, \"identifier\"],\n      [/\\*/, \"wildcard\"],\n      [/[\\.,]/, \"delimiter\"],\n      [\"\", \"\", \"@pop\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/(\\/\\/).*/, \"comment\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,EAC1B;AACF;AACA,IAAI,WAAW;AAAA;AAAA,EAEb,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,iBAAiB;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,QAAQ,SAAS,UAAU,UAAU,UAAU,UAAU,OAAO,MAAM;AAAA,EAClF,SAAS;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB,CAAC,aAAa,MAAM;AAAA,EACtC,gBAAgB,CAAC,QAAQ;AAAA,EACzB,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,qBAAqB;AAAA,cACnB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,mBAAmB;AAAA,cACjB,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,YACA,iBAAiB;AAAA,YACjB,aAAa;AAAA,YACb,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,oBAAoB;AAAA,YACpB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,YAAY,EAAE,OAAO,EAAE,cAAc,YAAY,YAAY,GAAG,EAAE,CAAC;AAAA;AAAA,MAEpE,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,SAAS,WAAW;AAAA;AAAA;AAAA,MAGrB,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,UAAU,CAAC;AAAA,IACpE;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,WAAW;AAAA,MACT,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,eAAe,WAAW;AAAA,MAC3B,CAAC,QAAQ,WAAW;AAAA,MACpB,CAAC,IAAI,IAAI,MAAM;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,qBAAqB,WAAW;AAAA,MACjC,CAAC,eAAe,YAAY;AAAA,MAC5B,CAAC,MAAM,UAAU;AAAA,MACjB,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,IAAI,IAAI,MAAM;AAAA,IACjB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,YAAY,SAAS;AAAA,IACxB;AAAA,EACF;AACF;", "names": []}
{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/sb/sb.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/sb/sb.ts\nvar conf = {\n  comments: {\n    lineComment: \"'\"\n  },\n  brackets: [\n    [\"(\", \")\"],\n    [\"[\", \"]\"],\n    [\"If\", \"EndIf\"],\n    [\"While\", \"EndWhile\"],\n    [\"For\", \"EndFor\"],\n    [\"Sub\", \"EndSub\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".sb\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.array\", open: \"[\", close: \"]\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    // Special bracket statement pairs\n    { token: \"keyword.tag-if\", open: \"If\", close: \"EndIf\" },\n    { token: \"keyword.tag-while\", open: \"While\", close: \"EndWhile\" },\n    { token: \"keyword.tag-for\", open: \"For\", close: \"EndFor\" },\n    { token: \"keyword.tag-sub\", open: \"Sub\", close: \"EndSub\" }\n  ],\n  keywords: [\n    \"Else\",\n    \"ElseIf\",\n    \"EndFor\",\n    \"EndIf\",\n    \"EndSub\",\n    \"EndWhile\",\n    \"For\",\n    \"Goto\",\n    \"If\",\n    \"Step\",\n    \"Sub\",\n    \"Then\",\n    \"To\",\n    \"While\"\n  ],\n  tagwords: [\"If\", \"Sub\", \"While\", \"For\"],\n  operators: [\">\", \"<\", \"<>\", \"<=\", \">=\", \"And\", \"Or\", \"+\", \"-\", \"*\", \"/\", \"=\"],\n  // we include these common regular expressions\n  identifier: /[a-zA-Z_][\\w]*/,\n  symbols: /[=><:+\\-*\\/%\\.,]+/,\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      // whitespace\n      { include: \"@whitespace\" },\n      // classes\n      [/(@identifier)(?=[.])/, \"type\"],\n      // identifiers, tagwords, and keywords\n      [\n        /@identifier/,\n        {\n          cases: {\n            \"@keywords\": { token: \"keyword.$0\" },\n            \"@operators\": \"operator\",\n            \"@default\": \"variable.name\"\n          }\n        }\n      ],\n      // methods, properties, and events\n      [\n        /([.])(@identifier)/,\n        {\n          cases: {\n            $2: [\"delimiter\", \"type.member\"],\n            \"@default\": \"\"\n          }\n        }\n      ],\n      // numbers\n      [/\\d*\\.\\d+/, \"number.float\"],\n      [/\\d+/, \"number\"],\n      // delimiters and operators\n      [/[()\\[\\]]/, \"@brackets\"],\n      [\n        /@symbols/,\n        {\n          cases: {\n            \"@operators\": \"operator\",\n            \"@default\": \"delimiter\"\n          }\n        }\n      ],\n      // strings\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, \"string\", \"@string\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"\"],\n      [/(\\').*$/, \"comment\"]\n    ],\n    string: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"C?/, \"string\", \"@pop\"]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,MAAM,OAAO;AAAA,IACd,CAAC,SAAS,UAAU;AAAA,IACpB,CAAC,OAAO,QAAQ;AAAA,IAChB,CAAC,OAAO,QAAQ;AAAA,EAClB;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA;AAAA,IAExD,EAAE,OAAO,kBAAkB,MAAM,MAAM,OAAO,QAAQ;AAAA,IACtD,EAAE,OAAO,qBAAqB,MAAM,SAAS,OAAO,WAAW;AAAA,IAC/D,EAAE,OAAO,mBAAmB,MAAM,OAAO,OAAO,SAAS;AAAA,IACzD,EAAE,OAAO,mBAAmB,MAAM,OAAO,OAAO,SAAS;AAAA,EAC3D;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU,CAAC,MAAM,OAAO,SAAS,KAAK;AAAA,EACtC,WAAW,CAAC,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,MAAM,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA;AAAA,EAE5E,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA;AAAA,MAEJ,EAAE,SAAS,cAAc;AAAA;AAAA,MAEzB,CAAC,wBAAwB,MAAM;AAAA;AAAA,MAE/B;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa,EAAE,OAAO,aAAa;AAAA,YACnC,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,IAAI,CAAC,aAAa,aAAa;AAAA,YAC/B,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,YAAY,cAAc;AAAA,MAC3B,CAAC,OAAO,QAAQ;AAAA;AAAA,MAEhB,CAAC,YAAY,WAAW;AAAA,MACxB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA;AAAA,MAEA,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,KAAK,UAAU,SAAS;AAAA,IAC3B;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,EAAE;AAAA,MACjB,CAAC,WAAW,SAAS;AAAA,IACvB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,OAAO,UAAU,MAAM;AAAA,IAC1B;AAAA,EACF;AACF;", "names": []}
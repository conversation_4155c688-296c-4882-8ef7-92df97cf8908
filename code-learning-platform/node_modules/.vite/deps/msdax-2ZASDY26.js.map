{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/msdax/msdax.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/msdax/msdax.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"/*\", \"*/\"]\n  },\n  brackets: [\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"{\", \"}\"]\n  ],\n  autoClosingPairs: [\n    { open: '\"', close: '\"', notIn: [\"string\", \"comment\"] },\n    { open: \"'\", close: \"'\", notIn: [\"string\", \"comment\"] },\n    { open: \"[\", close: \"]\", notIn: [\"string\", \"comment\"] },\n    { open: \"(\", close: \")\", notIn: [\"string\", \"comment\"] },\n    { open: \"{\", close: \"}\", notIn: [\"string\", \"comment\"] }\n  ]\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".msdax\",\n  ignoreCase: true,\n  brackets: [\n    { open: \"[\", close: \"]\", token: \"delimiter.square\" },\n    { open: \"{\", close: \"}\", token: \"delimiter.brackets\" },\n    { open: \"(\", close: \")\", token: \"delimiter.parenthesis\" }\n  ],\n  keywords: [\n    // Query keywords\n    \"VAR\",\n    \"RETURN\",\n    \"NOT\",\n    \"EVALUATE\",\n    \"DATATABLE\",\n    \"ORDER\",\n    \"BY\",\n    \"START\",\n    \"AT\",\n    \"DEFINE\",\n    \"MEASURE\",\n    \"ASC\",\n    \"DESC\",\n    \"IN\",\n    // Datatable types\n    \"BOOLEAN\",\n    \"DOUBLE\",\n    \"INTEGER\",\n    \"DATETIME\",\n    \"CURRENCY\",\n    \"STRING\"\n  ],\n  functions: [\n    // Relational\n    \"CLOSINGBALANCEMONTH\",\n    \"CLOSINGBALANCEQUARTER\",\n    \"CLOSINGBALANCEYEAR\",\n    \"DATEADD\",\n    \"DATESBETWEEN\",\n    \"DATESINPERIOD\",\n    \"DATESMTD\",\n    \"DATESQTD\",\n    \"DATESYTD\",\n    \"ENDOFMONTH\",\n    \"ENDOFQUARTER\",\n    \"ENDOFYEAR\",\n    \"FIRSTDATE\",\n    \"FIRSTNONBLANK\",\n    \"LASTDATE\",\n    \"LASTNONBLANK\",\n    \"NEXTDAY\",\n    \"NEXTMONTH\",\n    \"NEXTQUARTER\",\n    \"NEXTYEAR\",\n    \"OPENINGBALANCEMONTH\",\n    \"OPENINGBALANCEQUARTER\",\n    \"OPENINGBALANCEYEAR\",\n    \"PARALLELPERIOD\",\n    \"PREVIOUSDAY\",\n    \"PREVIOUSMONTH\",\n    \"PREVIOUSQUARTER\",\n    \"PREVIOUSYEAR\",\n    \"SAMEPERIODLASTYEAR\",\n    \"STARTOFMONTH\",\n    \"STARTOFQUARTER\",\n    \"STARTOFYEAR\",\n    \"TOTALMTD\",\n    \"TOTALQTD\",\n    \"TOTALYTD\",\n    \"ADDCOLUMNS\",\n    \"ADDMISSINGITEMS\",\n    \"ALL\",\n    \"ALLEXCEPT\",\n    \"ALLNOBLANKROW\",\n    \"ALLSELECTED\",\n    \"CALCULATE\",\n    \"CALCULATETABLE\",\n    \"CALENDAR\",\n    \"CALENDARAUTO\",\n    \"CROSSFILTER\",\n    \"CROSSJOIN\",\n    \"CURRENTGROUP\",\n    \"DATATABLE\",\n    \"DETAILROWS\",\n    \"DISTINCT\",\n    \"EARLIER\",\n    \"EARLIEST\",\n    \"EXCEPT\",\n    \"FILTER\",\n    \"FILTERS\",\n    \"GENERATE\",\n    \"GENERATEALL\",\n    \"GROUPBY\",\n    \"IGNORE\",\n    \"INTERSECT\",\n    \"ISONORAFTER\",\n    \"KEEPFILTERS\",\n    \"LOOKUPVALUE\",\n    \"NATURALINNERJOIN\",\n    \"NATURALLEFTOUTERJOIN\",\n    \"RELATED\",\n    \"RELATEDTABLE\",\n    \"ROLLUP\",\n    \"ROLLUPADDISSUBTOTAL\",\n    \"ROLLUPGROUP\",\n    \"ROLLUPISSUBTOTAL\",\n    \"ROW\",\n    \"SAMPLE\",\n    \"SELECTCOLUMNS\",\n    \"SUBSTITUTEWITHINDEX\",\n    \"SUMMARIZE\",\n    \"SUMMARIZECOLUMNS\",\n    \"TOPN\",\n    \"TREATAS\",\n    \"UNION\",\n    \"USERELATIONSHIP\",\n    \"VALUES\",\n    \"SUM\",\n    \"SUMX\",\n    \"PATH\",\n    \"PATHCONTAINS\",\n    \"PATHITEM\",\n    \"PATHITEMREVERSE\",\n    \"PATHLENGTH\",\n    \"AVERAGE\",\n    \"AVERAGEA\",\n    \"AVERAGEX\",\n    \"COUNT\",\n    \"COUNTA\",\n    \"COUNTAX\",\n    \"COUNTBLANK\",\n    \"COUNTROWS\",\n    \"COUNTX\",\n    \"DISTINCTCOUNT\",\n    \"DIVIDE\",\n    \"GEOMEAN\",\n    \"GEOMEANX\",\n    \"MAX\",\n    \"MAXA\",\n    \"MAXX\",\n    \"MEDIAN\",\n    \"MEDIANX\",\n    \"MIN\",\n    \"MINA\",\n    \"MINX\",\n    \"PERCENTILE.EXC\",\n    \"PERCENTILE.INC\",\n    \"PERCENTILEX.EXC\",\n    \"PERCENTILEX.INC\",\n    \"PRODUCT\",\n    \"PRODUCTX\",\n    \"RANK.EQ\",\n    \"RANKX\",\n    \"STDEV.P\",\n    \"STDEV.S\",\n    \"STDEVX.P\",\n    \"STDEVX.S\",\n    \"VAR.P\",\n    \"VAR.S\",\n    \"VARX.P\",\n    \"VARX.S\",\n    \"XIRR\",\n    \"XNPV\",\n    // Scalar\n    \"DATE\",\n    \"DATEDIFF\",\n    \"DATEVALUE\",\n    \"DAY\",\n    \"EDATE\",\n    \"EOMONTH\",\n    \"HOUR\",\n    \"MINUTE\",\n    \"MONTH\",\n    \"NOW\",\n    \"SECOND\",\n    \"TIME\",\n    \"TIMEVALUE\",\n    \"TODAY\",\n    \"WEEKDAY\",\n    \"WEEKNUM\",\n    \"YEAR\",\n    \"YEARFRAC\",\n    \"CONTAINS\",\n    \"CONTAINSROW\",\n    \"CUSTOMDATA\",\n    \"ERROR\",\n    \"HASONEFILTER\",\n    \"HASONEVALUE\",\n    \"ISBLANK\",\n    \"ISCROSSFILTERED\",\n    \"ISEMPTY\",\n    \"ISERROR\",\n    \"ISEVEN\",\n    \"ISFILTERED\",\n    \"ISLOGICAL\",\n    \"ISNONTEXT\",\n    \"ISNUMBER\",\n    \"ISODD\",\n    \"ISSUBTOTAL\",\n    \"ISTEXT\",\n    \"USERNAME\",\n    \"USERPRINCIPALNAME\",\n    \"AND\",\n    \"FALSE\",\n    \"IF\",\n    \"IFERROR\",\n    \"NOT\",\n    \"OR\",\n    \"SWITCH\",\n    \"TRUE\",\n    \"ABS\",\n    \"ACOS\",\n    \"ACOSH\",\n    \"ACOT\",\n    \"ACOTH\",\n    \"ASIN\",\n    \"ASINH\",\n    \"ATAN\",\n    \"ATANH\",\n    \"BETA.DIST\",\n    \"BETA.INV\",\n    \"CEILING\",\n    \"CHISQ.DIST\",\n    \"CHISQ.DIST.RT\",\n    \"CHISQ.INV\",\n    \"CHISQ.INV.RT\",\n    \"COMBIN\",\n    \"COMBINA\",\n    \"CONFIDENCE.NORM\",\n    \"CONFIDENCE.T\",\n    \"COS\",\n    \"COSH\",\n    \"COT\",\n    \"COTH\",\n    \"CURRENCY\",\n    \"DEGREES\",\n    \"EVEN\",\n    \"EXP\",\n    \"EXPON.DIST\",\n    \"FACT\",\n    \"FLOOR\",\n    \"GCD\",\n    \"INT\",\n    \"ISO.CEILING\",\n    \"LCM\",\n    \"LN\",\n    \"LOG\",\n    \"LOG10\",\n    \"MOD\",\n    \"MROUND\",\n    \"ODD\",\n    \"PERMUT\",\n    \"PI\",\n    \"POISSON.DIST\",\n    \"POWER\",\n    \"QUOTIENT\",\n    \"RADIANS\",\n    \"RAND\",\n    \"RANDBETWEEN\",\n    \"ROUND\",\n    \"ROUNDDOWN\",\n    \"ROUNDUP\",\n    \"SIGN\",\n    \"SIN\",\n    \"SINH\",\n    \"SQRT\",\n    \"SQRTPI\",\n    \"TAN\",\n    \"TANH\",\n    \"TRUNC\",\n    \"BLANK\",\n    \"CONCATENATE\",\n    \"CONCATENATEX\",\n    \"EXACT\",\n    \"FIND\",\n    \"FIXED\",\n    \"FORMAT\",\n    \"LEFT\",\n    \"LEN\",\n    \"LOWER\",\n    \"MID\",\n    \"REPLACE\",\n    \"REPT\",\n    \"RIGHT\",\n    \"SEARCH\",\n    \"SUBSTITUTE\",\n    \"TRIM\",\n    \"UNICHAR\",\n    \"UNICODE\",\n    \"UPPER\",\n    \"VALUE\"\n  ],\n  tokenizer: {\n    root: [\n      { include: \"@comments\" },\n      { include: \"@whitespace\" },\n      { include: \"@numbers\" },\n      { include: \"@strings\" },\n      { include: \"@complexIdentifiers\" },\n      [/[;,.]/, \"delimiter\"],\n      [/[({})]/, \"@brackets\"],\n      [\n        /[a-z_][a-zA-Z0-9_]*/,\n        {\n          cases: {\n            \"@keywords\": \"keyword\",\n            \"@functions\": \"keyword\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      [/[<>=!%&+\\-*/|~^]/, \"operator\"]\n    ],\n    whitespace: [[/\\s+/, \"white\"]],\n    comments: [\n      [/\\/\\/+.*/, \"comment\"],\n      [/\\/\\*/, { token: \"comment.quote\", next: \"@comment\" }]\n    ],\n    comment: [\n      [/[^*/]+/, \"comment\"],\n      [/\\*\\//, { token: \"comment.quote\", next: \"@pop\" }],\n      [/./, \"comment\"]\n    ],\n    numbers: [\n      [/0[xX][0-9a-fA-F]*/, \"number\"],\n      [/[$][+-]*\\d*(\\.\\d*)?/, \"number\"],\n      [/((\\d+(\\.\\d*)?)|(\\.\\d+))([eE][\\-+]?\\d+)?/, \"number\"]\n    ],\n    strings: [\n      [/N\"/, { token: \"string\", next: \"@string\" }],\n      [/\"/, { token: \"string\", next: \"@string\" }]\n    ],\n    string: [\n      [/[^\"]+/, \"string\"],\n      [/\"\"/, \"string\"],\n      [/\"/, { token: \"string\", next: \"@pop\" }]\n    ],\n    complexIdentifiers: [\n      [/\\[/, { token: \"identifier.quote\", next: \"@bracketedIdentifier\" }],\n      [/'/, { token: \"identifier.quote\", next: \"@quotedIdentifier\" }]\n    ],\n    bracketedIdentifier: [\n      [/[^\\]]+/, \"identifier\"],\n      [/]]/, \"identifier\"],\n      [/]/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ],\n    quotedIdentifier: [\n      [/[^']+/, \"identifier\"],\n      [/''/, \"identifier\"],\n      [/'/, { token: \"identifier.quote\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,EACX;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,IACtD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,UAAU,SAAS,EAAE;AAAA,EACxD;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,mBAAmB;AAAA,IACnD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,qBAAqB;AAAA,IACrD,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,wBAAwB;AAAA,EAC1D;AAAA,EACA,UAAU;AAAA;AAAA,IAER;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA;AAAA,IAET;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,EAAE,SAAS,YAAY;AAAA,MACvB,EAAE,SAAS,cAAc;AAAA,MACzB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,WAAW;AAAA,MACtB,EAAE,SAAS,sBAAsB;AAAA,MACjC,CAAC,SAAS,WAAW;AAAA,MACrB,CAAC,UAAU,WAAW;AAAA,MACtB;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,aAAa;AAAA,YACb,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,CAAC,oBAAoB,UAAU;AAAA,IACjC;AAAA,IACA,YAAY,CAAC,CAAC,OAAO,OAAO,CAAC;AAAA,IAC7B,UAAU;AAAA,MACR,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,WAAW,CAAC;AAAA,IACvD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,UAAU,SAAS;AAAA,MACpB,CAAC,QAAQ,EAAE,OAAO,iBAAiB,MAAM,OAAO,CAAC;AAAA,MACjD,CAAC,KAAK,SAAS;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,CAAC,qBAAqB,QAAQ;AAAA,MAC9B,CAAC,uBAAuB,QAAQ;AAAA,MAChC,CAAC,2CAA2C,QAAQ;AAAA,IACtD;AAAA,IACA,SAAS;AAAA,MACP,CAAC,MAAM,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,MAC3C,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,UAAU,CAAC;AAAA,IAC5C;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,SAAS,QAAQ;AAAA,MAClB,CAAC,MAAM,QAAQ;AAAA,MACf,CAAC,KAAK,EAAE,OAAO,UAAU,MAAM,OAAO,CAAC;AAAA,IACzC;AAAA,IACA,oBAAoB;AAAA,MAClB,CAAC,MAAM,EAAE,OAAO,oBAAoB,MAAM,uBAAuB,CAAC;AAAA,MAClE,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,oBAAoB,CAAC;AAAA,IAChE;AAAA,IACA,qBAAqB;AAAA,MACnB,CAAC,UAAU,YAAY;AAAA,MACvB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,IACA,kBAAkB;AAAA,MAChB,CAAC,SAAS,YAAY;AAAA,MACtB,CAAC,MAAM,YAAY;AAAA,MACnB,CAAC,KAAK,EAAE,OAAO,oBAAoB,MAAM,OAAO,CAAC;AAAA,IACnD;AAAA,EACF;AACF;", "names": []}
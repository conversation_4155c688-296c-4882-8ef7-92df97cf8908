{"version": 3, "sources": ["../../monaco-editor/esm/vs/basic-languages/st/st.js"], "sourcesContent": ["/*!-----------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)\n * Released under the MIT license\n * https://github.com/microsoft/monaco-editor/blob/main/LICENSE.txt\n *-----------------------------------------------------------------------------*/\n\n\n// src/basic-languages/st/st.ts\nvar conf = {\n  comments: {\n    lineComment: \"//\",\n    blockComment: [\"(*\", \"*)\"]\n  },\n  brackets: [\n    [\"{\", \"}\"],\n    [\"[\", \"]\"],\n    [\"(\", \")\"],\n    [\"var\", \"end_var\"],\n    [\"var_input\", \"end_var\"],\n    [\"var_output\", \"end_var\"],\n    [\"var_in_out\", \"end_var\"],\n    [\"var_temp\", \"end_var\"],\n    [\"var_global\", \"end_var\"],\n    [\"var_access\", \"end_var\"],\n    [\"var_external\", \"end_var\"],\n    [\"type\", \"end_type\"],\n    [\"struct\", \"end_struct\"],\n    [\"program\", \"end_program\"],\n    [\"function\", \"end_function\"],\n    [\"function_block\", \"end_function_block\"],\n    [\"action\", \"end_action\"],\n    [\"step\", \"end_step\"],\n    [\"initial_step\", \"end_step\"],\n    [\"transaction\", \"end_transaction\"],\n    [\"configuration\", \"end_configuration\"],\n    [\"tcp\", \"end_tcp\"],\n    [\"recource\", \"end_recource\"],\n    [\"channel\", \"end_channel\"],\n    [\"library\", \"end_library\"],\n    [\"folder\", \"end_folder\"],\n    [\"binaries\", \"end_binaries\"],\n    [\"includes\", \"end_includes\"],\n    [\"sources\", \"end_sources\"]\n  ],\n  autoClosingPairs: [\n    { open: \"[\", close: \"]\" },\n    { open: \"{\", close: \"}\" },\n    { open: \"(\", close: \")\" },\n    { open: \"/*\", close: \"*/\" },\n    { open: \"'\", close: \"'\", notIn: [\"string_sq\"] },\n    { open: '\"', close: '\"', notIn: [\"string_dq\"] },\n    { open: \"var_input\", close: \"end_var\" },\n    { open: \"var_output\", close: \"end_var\" },\n    { open: \"var_in_out\", close: \"end_var\" },\n    { open: \"var_temp\", close: \"end_var\" },\n    { open: \"var_global\", close: \"end_var\" },\n    { open: \"var_access\", close: \"end_var\" },\n    { open: \"var_external\", close: \"end_var\" },\n    { open: \"type\", close: \"end_type\" },\n    { open: \"struct\", close: \"end_struct\" },\n    { open: \"program\", close: \"end_program\" },\n    { open: \"function\", close: \"end_function\" },\n    { open: \"function_block\", close: \"end_function_block\" },\n    { open: \"action\", close: \"end_action\" },\n    { open: \"step\", close: \"end_step\" },\n    { open: \"initial_step\", close: \"end_step\" },\n    { open: \"transaction\", close: \"end_transaction\" },\n    { open: \"configuration\", close: \"end_configuration\" },\n    { open: \"tcp\", close: \"end_tcp\" },\n    { open: \"recource\", close: \"end_recource\" },\n    { open: \"channel\", close: \"end_channel\" },\n    { open: \"library\", close: \"end_library\" },\n    { open: \"folder\", close: \"end_folder\" },\n    { open: \"binaries\", close: \"end_binaries\" },\n    { open: \"includes\", close: \"end_includes\" },\n    { open: \"sources\", close: \"end_sources\" }\n  ],\n  surroundingPairs: [\n    { open: \"{\", close: \"}\" },\n    { open: \"[\", close: \"]\" },\n    { open: \"(\", close: \")\" },\n    { open: '\"', close: '\"' },\n    { open: \"'\", close: \"'\" },\n    { open: \"var\", close: \"end_var\" },\n    { open: \"var_input\", close: \"end_var\" },\n    { open: \"var_output\", close: \"end_var\" },\n    { open: \"var_in_out\", close: \"end_var\" },\n    { open: \"var_temp\", close: \"end_var\" },\n    { open: \"var_global\", close: \"end_var\" },\n    { open: \"var_access\", close: \"end_var\" },\n    { open: \"var_external\", close: \"end_var\" },\n    { open: \"type\", close: \"end_type\" },\n    { open: \"struct\", close: \"end_struct\" },\n    { open: \"program\", close: \"end_program\" },\n    { open: \"function\", close: \"end_function\" },\n    { open: \"function_block\", close: \"end_function_block\" },\n    { open: \"action\", close: \"end_action\" },\n    { open: \"step\", close: \"end_step\" },\n    { open: \"initial_step\", close: \"end_step\" },\n    { open: \"transaction\", close: \"end_transaction\" },\n    { open: \"configuration\", close: \"end_configuration\" },\n    { open: \"tcp\", close: \"end_tcp\" },\n    { open: \"recource\", close: \"end_recource\" },\n    { open: \"channel\", close: \"end_channel\" },\n    { open: \"library\", close: \"end_library\" },\n    { open: \"folder\", close: \"end_folder\" },\n    { open: \"binaries\", close: \"end_binaries\" },\n    { open: \"includes\", close: \"end_includes\" },\n    { open: \"sources\", close: \"end_sources\" }\n  ],\n  folding: {\n    markers: {\n      start: new RegExp(\"^\\\\s*#pragma\\\\s+region\\\\b\"),\n      end: new RegExp(\"^\\\\s*#pragma\\\\s+endregion\\\\b\")\n    }\n  }\n};\nvar language = {\n  defaultToken: \"\",\n  tokenPostfix: \".st\",\n  ignoreCase: true,\n  brackets: [\n    { token: \"delimiter.curly\", open: \"{\", close: \"}\" },\n    { token: \"delimiter.parenthesis\", open: \"(\", close: \")\" },\n    { token: \"delimiter.square\", open: \"[\", close: \"]\" }\n  ],\n  keywords: [\n    \"if\",\n    \"end_if\",\n    \"elsif\",\n    \"else\",\n    \"case\",\n    \"of\",\n    \"to\",\n    \"__try\",\n    \"__catch\",\n    \"__finally\",\n    \"do\",\n    \"with\",\n    \"by\",\n    \"while\",\n    \"repeat\",\n    \"end_while\",\n    \"end_repeat\",\n    \"end_case\",\n    \"for\",\n    \"end_for\",\n    \"task\",\n    \"retain\",\n    \"non_retain\",\n    \"constant\",\n    \"with\",\n    \"at\",\n    \"exit\",\n    \"return\",\n    \"interval\",\n    \"priority\",\n    \"address\",\n    \"port\",\n    \"on_channel\",\n    \"then\",\n    \"iec\",\n    \"file\",\n    \"uses\",\n    \"version\",\n    \"packagetype\",\n    \"displayname\",\n    \"copyright\",\n    \"summary\",\n    \"vendor\",\n    \"common_source\",\n    \"from\",\n    \"extends\",\n    \"implements\"\n  ],\n  constant: [\"false\", \"true\", \"null\"],\n  defineKeywords: [\n    \"var\",\n    \"var_input\",\n    \"var_output\",\n    \"var_in_out\",\n    \"var_temp\",\n    \"var_global\",\n    \"var_access\",\n    \"var_external\",\n    \"end_var\",\n    \"type\",\n    \"end_type\",\n    \"struct\",\n    \"end_struct\",\n    \"program\",\n    \"end_program\",\n    \"function\",\n    \"end_function\",\n    \"function_block\",\n    \"end_function_block\",\n    \"interface\",\n    \"end_interface\",\n    \"method\",\n    \"end_method\",\n    \"property\",\n    \"end_property\",\n    \"namespace\",\n    \"end_namespace\",\n    \"configuration\",\n    \"end_configuration\",\n    \"tcp\",\n    \"end_tcp\",\n    \"resource\",\n    \"end_resource\",\n    \"channel\",\n    \"end_channel\",\n    \"library\",\n    \"end_library\",\n    \"folder\",\n    \"end_folder\",\n    \"binaries\",\n    \"end_binaries\",\n    \"includes\",\n    \"end_includes\",\n    \"sources\",\n    \"end_sources\",\n    \"action\",\n    \"end_action\",\n    \"step\",\n    \"initial_step\",\n    \"end_step\",\n    \"transaction\",\n    \"end_transaction\"\n  ],\n  typeKeywords: [\n    \"int\",\n    \"sint\",\n    \"dint\",\n    \"lint\",\n    \"usint\",\n    \"uint\",\n    \"udint\",\n    \"ulint\",\n    \"real\",\n    \"lreal\",\n    \"time\",\n    \"date\",\n    \"time_of_day\",\n    \"date_and_time\",\n    \"string\",\n    \"bool\",\n    \"byte\",\n    \"word\",\n    \"dword\",\n    \"array\",\n    \"pointer\",\n    \"lword\"\n  ],\n  operators: [\n    \"=\",\n    \">\",\n    \"<\",\n    \":\",\n    \":=\",\n    \"<=\",\n    \">=\",\n    \"<>\",\n    \"&\",\n    \"+\",\n    \"-\",\n    \"*\",\n    \"**\",\n    \"MOD\",\n    \"^\",\n    \"or\",\n    \"and\",\n    \"not\",\n    \"xor\",\n    \"abs\",\n    \"acos\",\n    \"asin\",\n    \"atan\",\n    \"cos\",\n    \"exp\",\n    \"expt\",\n    \"ln\",\n    \"log\",\n    \"sin\",\n    \"sqrt\",\n    \"tan\",\n    \"sel\",\n    \"max\",\n    \"min\",\n    \"limit\",\n    \"mux\",\n    \"shl\",\n    \"shr\",\n    \"rol\",\n    \"ror\",\n    \"indexof\",\n    \"sizeof\",\n    \"adr\",\n    \"adrinst\",\n    \"bitadr\",\n    \"is_valid\",\n    \"ref\",\n    \"ref_to\"\n  ],\n  builtinVariables: [],\n  builtinFunctions: [\n    \"sr\",\n    \"rs\",\n    \"tp\",\n    \"ton\",\n    \"tof\",\n    \"eq\",\n    \"ge\",\n    \"le\",\n    \"lt\",\n    \"ne\",\n    \"round\",\n    \"trunc\",\n    \"ctd\",\n    \"\\u0441tu\",\n    \"ctud\",\n    \"r_trig\",\n    \"f_trig\",\n    \"move\",\n    \"concat\",\n    \"delete\",\n    \"find\",\n    \"insert\",\n    \"left\",\n    \"len\",\n    \"replace\",\n    \"right\",\n    \"rtc\"\n  ],\n  // we include these common regular expressions\n  symbols: /[=><!~?:&|+\\-*\\/\\^%]+/,\n  // C# style strings\n  escapes: /\\\\(?:[abfnrtv\\\\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,\n  // The main tokenizer for our languages\n  tokenizer: {\n    root: [\n      [/(\\.\\.)/, \"delimiter\"],\n      [/\\b(16#[0-9A-Fa-f\\_]*)+\\b/, \"number.hex\"],\n      [/\\b(2#[01\\_]+)+\\b/, \"number.binary\"],\n      [/\\b(8#[0-9\\_]*)+\\b/, \"number.octal\"],\n      [/\\b\\d*\\.\\d+([eE][\\-+]?\\d+)?\\b/, \"number.float\"],\n      [/\\b(L?REAL)#[0-9\\_\\.e]+\\b/, \"number.float\"],\n      [/\\b(BYTE|(?:D|L)?WORD|U?(?:S|D|L)?INT)#[0-9\\_]+\\b/, \"number\"],\n      [/\\d+/, \"number\"],\n      [/\\b(T|DT|TOD)#[0-9:-_shmyd]+\\b/, \"tag\"],\n      [/\\%(I|Q|M)(X|B|W|D|L)[0-9\\.]+/, \"tag\"],\n      [/\\%(I|Q|M)[0-9\\.]*/, \"tag\"],\n      [/\\b[A-Za-z]{1,6}#[0-9]+\\b/, \"tag\"],\n      [/\\b(TO_|CTU_|CTD_|CTUD_|MUX_|SEL_)[A_Za-z]+\\b/, \"predefined\"],\n      [/\\b[A_Za-z]+(_TO_)[A_Za-z]+\\b/, \"predefined\"],\n      [/[;]/, \"delimiter\"],\n      [/[.]/, { token: \"delimiter\", next: \"@params\" }],\n      // identifiers and keywords\n      [\n        /[a-zA-Z_]\\w*/,\n        {\n          cases: {\n            \"@operators\": \"operators\",\n            \"@keywords\": \"keyword\",\n            \"@typeKeywords\": \"type\",\n            \"@defineKeywords\": \"variable\",\n            \"@constant\": \"constant\",\n            \"@builtinVariables\": \"predefined\",\n            \"@builtinFunctions\": \"predefined\",\n            \"@default\": \"identifier\"\n          }\n        }\n      ],\n      { include: \"@whitespace\" },\n      [/[{}()\\[\\]]/, \"@brackets\"],\n      [/\"([^\"\\\\]|\\\\.)*$/, \"string.invalid\"],\n      // non-teminated string\n      [/\"/, { token: \"string.quote\", bracket: \"@open\", next: \"@string_dq\" }],\n      [/'/, { token: \"string.quote\", bracket: \"@open\", next: \"@string_sq\" }],\n      [/'[^\\\\']'/, \"string\"],\n      [/(')(@escapes)(')/, [\"string\", \"string.escape\", \"string\"]],\n      [/'/, \"string.invalid\"]\n    ],\n    params: [\n      [/\\b[A-Za-z0-9_]+\\b(?=\\()/, { token: \"identifier\", next: \"@pop\" }],\n      [/\\b[A-Za-z0-9_]+\\b/, \"variable.name\", \"@pop\"]\n    ],\n    comment: [\n      [/[^\\/*]+/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*/\", \"comment\", \"@pop\"],\n      [/[\\/*]/, \"comment\"]\n    ],\n    comment2: [\n      [/[^\\(*]+/, \"comment\"],\n      [/\\(\\*/, \"comment\", \"@push\"],\n      // nested comment\n      [\"\\\\*\\\\)\", \"comment\", \"@pop\"],\n      [/[\\(*]/, \"comment\"]\n    ],\n    whitespace: [\n      [/[ \\t\\r\\n]+/, \"white\"],\n      [/\\/\\/.*$/, \"comment\"],\n      [/\\/\\*/, \"comment\", \"@comment\"],\n      [/\\(\\*/, \"comment\", \"@comment2\"]\n    ],\n    string_dq: [\n      [/[^\\\\\"]+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/\"/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ],\n    string_sq: [\n      [/[^\\\\']+/, \"string\"],\n      [/@escapes/, \"string.escape\"],\n      [/\\\\./, \"string.escape.invalid\"],\n      [/'/, { token: \"string.quote\", bracket: \"@close\", next: \"@pop\" }]\n    ]\n  }\n};\nexport {\n  conf,\n  language\n};\n"], "mappings": ";;;AASA,IAAI,OAAO;AAAA,EACT,UAAU;AAAA,IACR,aAAa;AAAA,IACb,cAAc,CAAC,MAAM,IAAI;AAAA,EAC3B;AAAA,EACA,UAAU;AAAA,IACR,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,KAAK,GAAG;AAAA,IACT,CAAC,OAAO,SAAS;AAAA,IACjB,CAAC,aAAa,SAAS;AAAA,IACvB,CAAC,cAAc,SAAS;AAAA,IACxB,CAAC,cAAc,SAAS;AAAA,IACxB,CAAC,YAAY,SAAS;AAAA,IACtB,CAAC,cAAc,SAAS;AAAA,IACxB,CAAC,cAAc,SAAS;AAAA,IACxB,CAAC,gBAAgB,SAAS;AAAA,IAC1B,CAAC,QAAQ,UAAU;AAAA,IACnB,CAAC,UAAU,YAAY;AAAA,IACvB,CAAC,WAAW,aAAa;AAAA,IACzB,CAAC,YAAY,cAAc;AAAA,IAC3B,CAAC,kBAAkB,oBAAoB;AAAA,IACvC,CAAC,UAAU,YAAY;AAAA,IACvB,CAAC,QAAQ,UAAU;AAAA,IACnB,CAAC,gBAAgB,UAAU;AAAA,IAC3B,CAAC,eAAe,iBAAiB;AAAA,IACjC,CAAC,iBAAiB,mBAAmB;AAAA,IACrC,CAAC,OAAO,SAAS;AAAA,IACjB,CAAC,YAAY,cAAc;AAAA,IAC3B,CAAC,WAAW,aAAa;AAAA,IACzB,CAAC,WAAW,aAAa;AAAA,IACzB,CAAC,UAAU,YAAY;AAAA,IACvB,CAAC,YAAY,cAAc;AAAA,IAC3B,CAAC,YAAY,cAAc;AAAA,IAC3B,CAAC,WAAW,aAAa;AAAA,EAC3B;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,MAAM,OAAO,KAAK;AAAA,IAC1B,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,WAAW,EAAE;AAAA,IAC9C,EAAE,MAAM,KAAK,OAAO,KAAK,OAAO,CAAC,WAAW,EAAE;AAAA,IAC9C,EAAE,MAAM,aAAa,OAAO,UAAU;AAAA,IACtC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,YAAY,OAAO,UAAU;AAAA,IACrC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,gBAAgB,OAAO,UAAU;AAAA,IACzC,EAAE,MAAM,QAAQ,OAAO,WAAW;AAAA,IAClC,EAAE,MAAM,UAAU,OAAO,aAAa;AAAA,IACtC,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,IACxC,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,kBAAkB,OAAO,qBAAqB;AAAA,IACtD,EAAE,MAAM,UAAU,OAAO,aAAa;AAAA,IACtC,EAAE,MAAM,QAAQ,OAAO,WAAW;AAAA,IAClC,EAAE,MAAM,gBAAgB,OAAO,WAAW;AAAA,IAC1C,EAAE,MAAM,eAAe,OAAO,kBAAkB;AAAA,IAChD,EAAE,MAAM,iBAAiB,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,OAAO,OAAO,UAAU;AAAA,IAChC,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,IACxC,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,IACxC,EAAE,MAAM,UAAU,OAAO,aAAa;AAAA,IACtC,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,EAC1C;AAAA,EACA,kBAAkB;AAAA,IAChB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,KAAK,OAAO,IAAI;AAAA,IACxB,EAAE,MAAM,OAAO,OAAO,UAAU;AAAA,IAChC,EAAE,MAAM,aAAa,OAAO,UAAU;AAAA,IACtC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,YAAY,OAAO,UAAU;AAAA,IACrC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,cAAc,OAAO,UAAU;AAAA,IACvC,EAAE,MAAM,gBAAgB,OAAO,UAAU;AAAA,IACzC,EAAE,MAAM,QAAQ,OAAO,WAAW;AAAA,IAClC,EAAE,MAAM,UAAU,OAAO,aAAa;AAAA,IACtC,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,IACxC,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,kBAAkB,OAAO,qBAAqB;AAAA,IACtD,EAAE,MAAM,UAAU,OAAO,aAAa;AAAA,IACtC,EAAE,MAAM,QAAQ,OAAO,WAAW;AAAA,IAClC,EAAE,MAAM,gBAAgB,OAAO,WAAW;AAAA,IAC1C,EAAE,MAAM,eAAe,OAAO,kBAAkB;AAAA,IAChD,EAAE,MAAM,iBAAiB,OAAO,oBAAoB;AAAA,IACpD,EAAE,MAAM,OAAO,OAAO,UAAU;AAAA,IAChC,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,IACxC,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,IACxC,EAAE,MAAM,UAAU,OAAO,aAAa;AAAA,IACtC,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,YAAY,OAAO,eAAe;AAAA,IAC1C,EAAE,MAAM,WAAW,OAAO,cAAc;AAAA,EAC1C;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,MACP,OAAO,IAAI,OAAO,2BAA2B;AAAA,MAC7C,KAAK,IAAI,OAAO,8BAA8B;AAAA,IAChD;AAAA,EACF;AACF;AACA,IAAI,WAAW;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,EAAE,OAAO,mBAAmB,MAAM,KAAK,OAAO,IAAI;AAAA,IAClD,EAAE,OAAO,yBAAyB,MAAM,KAAK,OAAO,IAAI;AAAA,IACxD,EAAE,OAAO,oBAAoB,MAAM,KAAK,OAAO,IAAI;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,UAAU,CAAC,SAAS,QAAQ,MAAM;AAAA,EAClC,gBAAgB;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,cAAc;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,kBAAkB,CAAC;AAAA,EACnB,kBAAkB;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,SAAS;AAAA;AAAA,EAET,WAAW;AAAA,IACT,MAAM;AAAA,MACJ,CAAC,UAAU,WAAW;AAAA,MACtB,CAAC,4BAA4B,YAAY;AAAA,MACzC,CAAC,oBAAoB,eAAe;AAAA,MACpC,CAAC,qBAAqB,cAAc;AAAA,MACpC,CAAC,gCAAgC,cAAc;AAAA,MAC/C,CAAC,4BAA4B,cAAc;AAAA,MAC3C,CAAC,oDAAoD,QAAQ;AAAA,MAC7D,CAAC,OAAO,QAAQ;AAAA,MAChB,CAAC,iCAAiC,KAAK;AAAA,MACvC,CAAC,gCAAgC,KAAK;AAAA,MACtC,CAAC,qBAAqB,KAAK;AAAA,MAC3B,CAAC,4BAA4B,KAAK;AAAA,MAClC,CAAC,gDAAgD,YAAY;AAAA,MAC7D,CAAC,gCAAgC,YAAY;AAAA,MAC7C,CAAC,OAAO,WAAW;AAAA,MACnB,CAAC,OAAO,EAAE,OAAO,aAAa,MAAM,UAAU,CAAC;AAAA;AAAA,MAE/C;AAAA,QACE;AAAA,QACA;AAAA,UACE,OAAO;AAAA,YACL,cAAc;AAAA,YACd,aAAa;AAAA,YACb,iBAAiB;AAAA,YACjB,mBAAmB;AAAA,YACnB,aAAa;AAAA,YACb,qBAAqB;AAAA,YACrB,qBAAqB;AAAA,YACrB,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,SAAS,cAAc;AAAA,MACzB,CAAC,cAAc,WAAW;AAAA,MAC1B,CAAC,mBAAmB,gBAAgB;AAAA;AAAA,MAEpC,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,MACrE,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,SAAS,MAAM,aAAa,CAAC;AAAA,MACrE,CAAC,YAAY,QAAQ;AAAA,MACrB,CAAC,oBAAoB,CAAC,UAAU,iBAAiB,QAAQ,CAAC;AAAA,MAC1D,CAAC,KAAK,gBAAgB;AAAA,IACxB;AAAA,IACA,QAAQ;AAAA,MACN,CAAC,2BAA2B,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AAAA,MACjE,CAAC,qBAAqB,iBAAiB,MAAM;AAAA,IAC/C;AAAA,IACA,SAAS;AAAA,MACP,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,OAAO;AAAA;AAAA,MAE3B,CAAC,QAAQ,WAAW,MAAM;AAAA,MAC1B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,UAAU;AAAA,MACR,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,OAAO;AAAA;AAAA,MAE3B,CAAC,UAAU,WAAW,MAAM;AAAA,MAC5B,CAAC,SAAS,SAAS;AAAA,IACrB;AAAA,IACA,YAAY;AAAA,MACV,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,WAAW,SAAS;AAAA,MACrB,CAAC,QAAQ,WAAW,UAAU;AAAA,MAC9B,CAAC,QAAQ,WAAW,WAAW;AAAA,IACjC;AAAA,IACA,WAAW;AAAA,MACT,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,IACA,WAAW;AAAA,MACT,CAAC,WAAW,QAAQ;AAAA,MACpB,CAAC,YAAY,eAAe;AAAA,MAC5B,CAAC,OAAO,uBAAuB;AAAA,MAC/B,CAAC,KAAK,EAAE,OAAO,gBAAgB,SAAS,UAAU,MAAM,OAAO,CAAC;AAAA,IAClE;AAAA,EACF;AACF;", "names": []}